<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .wrapper {
            display: flex;
        }

        .author {
            color: blue;
        }
    </style>
</head>

<body>
    <div>
        <blockquote id="myBlockquote" contenteditable="true">
            <p>Edit this content to add your own quote</p>
            <div class="wrapper">
                <div class="author">@张三</div>
                <div class="author">@里斯</div>
                <span class="author">@王五</span>
            </div>
        </blockquote>
    </div>
    <script type="module">
        // 假设你的 blockquote 有 id="myBlockquote"
        const blockquote = document.getElementById('myBlockquote');

        blockquote.addEventListener('keydown', function (event) {
            // 检查是否按下了删除键
            if (event.key === 'Backspace' || event.key === 'Delete') {
                event.preventDefault();
                handleDelete();
            }
        });

        function handleDelete() {
            // 这里写你要执行的逻辑
            console.log('删除键被按下');
            const selection = window.getSelection();
            console.log('selection',selection.toString(), selection)
            if (!selection.rangeCount) return;
            const range = selection.getRangeAt(0);
            console.log(range)
            let node = range.startContainer;
            console.log('node', node)
            while (node && node !== blockquote) {
                if (node.classList && node.classList.contains('author')) {
                    // 删除整个 author 元素
                    const toRemove = node;
                    const parent = toRemove.parentNode;
                    parent.removeChild(toRemove);
                    // 可选：将光标移到下一个 author 或父元素
                    event.preventDefault();
                    return;
                }
                node = node.parentNode;
            }
        }
    </script>
</body>

</html>