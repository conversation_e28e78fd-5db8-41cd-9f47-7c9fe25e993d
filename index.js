// express-proxy.js
const express = require("express");
const { createProxyMiddleware } = require("http-proxy-middleware");

const app = express();
const proxy = createProxyMiddleware({
  target: "http://192.168.110.112:8099",
  changeOrigin: true,
  onProxyRes: (proxyRes) => {
    // 添加 CORS 头
    proxyRes.headers["Access-Control-Allow-Origin"] = "*";
    proxyRes.headers["Access-Control-Allow-Methods"] =
      "GET, POST, PUT, DELETE, OPTIONS";
    proxyRes.headers["Access-Control-Allow-Headers"] =
      "Content-Type, Authorization";
  },
});

// 配置代理中间件
app.use("/tiny/management/", proxy);

// 创建 HTTPS 服务器
app.listen(8082, () => {
  console.log("Express proxy server running on https://localhost:8088");
});
