// express-proxy.js
const express = require("express");
const { createProxyMiddleware } = require("http-proxy-middleware");

const PORT = 8083;

const app = express();

// 添加全局 CORS 中间件
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma");
  res.header("Access-Control-Allow-Credentials", "true");
  res.header("Access-Control-Max-Age", "86400"); // 24小时预检缓存

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

const proxy = createProxyMiddleware({
  target: "http://192.168.110.112:8099/",
  changeOrigin: true,
  onProxyRes: (proxyRes, req, res) => {
    // 确保代理响应也包含 CORS 头
    proxyRes.headers["Access-Control-Allow-Origin"] = "*";
    proxyRes.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH";
    proxyRes.headers["Access-Control-Allow-Headers"] = "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma";
    proxyRes.headers["Access-Control-Allow-Credentials"] = "true";
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err);
    res.status(500).json({ error: '代理服务器错误' });
  }
});

// 配置代理中间件
app.use("/tiny/management/", proxy);

// 创建 HTTPS 服务器
app.listen(PORT, () => {
  console.log(`Express proxy server running on https://localhost:${PORT}`);
});
